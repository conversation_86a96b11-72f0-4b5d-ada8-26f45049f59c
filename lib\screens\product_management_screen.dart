import 'dart:async';
import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../services/odoo_service.dart';
import '../config/ui_flags.dart';
import '../config/feature_flags.dart';
import '../widgets/product_image_picker_panel.dart';
import '../services/odoo_status_sync_service.dart';
import '../widgets/product_filter_widget.dart';

class ProductManagementScreen extends StatefulWidget {
  final Map<String, dynamic> seller;

  const ProductManagementScreen({super.key, required this.seller});

  @override
  State<ProductManagementScreen> createState() =>
      _ProductManagementScreenState();
}

class _ProductManagementScreenState extends State<ProductManagementScreen>
    with SingleTickerProviderStateMixin {
  final SupabaseService _supabaseService = SupabaseService();
  final OdooService _odooService = OdooService();
  final OdooStatusSyncService _syncService = OdooStatusSyncService();

  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _products = [];
  String _selectedFilter = 'all';
  ProductFilter _currentFilter = ProductFilter();

  // Background sync automation
  Timer? _backgroundSyncTimer;
  bool _isBackgroundSyncEnabled = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Feature-flagged auto status sync on open (zero-risk)
    FeatureFlags.isEnabledRemote('auto_sync_status_on_open').then((
      enabled,
    ) async {
      if (enabled) {
        try {
          await _syncService.syncAllProductStatus(
            sellerId: widget.seller['id'],
            showLogs: true,
          );
        } catch (_) {}
      }
      _loadProducts();
    });

    // Initialize background sync automation
    _initializeBackgroundSync();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _backgroundSyncTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadProducts() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final products = await _supabaseService.getMeatProducts(
        sellerId: widget.seller['id'],
        sortBy: _currentFilter.sortBy,
        ascending: _currentFilter.ascending,
        searchQuery: _currentFilter.searchQuery.isNotEmpty
            ? _currentFilter.searchQuery
            : null,
        approvalStatus: _currentFilter.approvalStatus,
        isActive: _currentFilter.isActive,
        limit: 100,
      );

      if (mounted) {
        setState(() {
          _products = products;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading products: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  List<Map<String, dynamic>> get _filteredProducts {
    // Apply tab-based filtering on top of server-side filtering
    switch (_selectedFilter) {
      case 'active':
        return _products.where((p) => p['is_active'] == true).toList();
      case 'pending':
        return _products
            .where((p) => p['approval_status'] == 'pending')
            .toList();
      case 'approved':
        return _products
            .where((p) => p['approval_status'] == 'approved')
            .toList();
      case 'rejected':
        return _products
            .where((p) => p['approval_status'] == 'rejected')
            .toList();
      default:
        return _products;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFFECFDF5), // emerald-50
              const Color(0xFFDCFAE6), // green-100
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              _buildFilterTabs(),
              ProductFilterWidget(
                currentFilter: _currentFilter,
                onFilterChanged: _onFilterChanged,
              ),
              Expanded(
                child: _isLoading ? _buildLoadingState() : _buildProductList(),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showAddProductDialog();
        },
        backgroundColor: const Color(0xFF059669),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Add Product'),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFF059669), const Color(0xFF047857)],
        ),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Product Management',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_products.length} products',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.sync, color: Colors.white),
            onPressed: _syncWithOdoo,
            tooltip: 'Sync with Odoo',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _refreshProducts,
            tooltip: 'Refresh Products',
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        onTap: (index) {
          setState(() {
            switch (index) {
              case 0:
                _selectedFilter = 'all';
                break;
              case 1:
                _selectedFilter = 'active';
                break;
              case 2:
                _selectedFilter = 'pending';
                break;
              case 3:
                _selectedFilter = 'approved';
                break;
            }
          });
        },
        labelColor: const Color(0xFF059669),
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: const Color(0xFF059669),
        indicatorWeight: 3,
        tabs: [
          Tab(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('All', style: TextStyle(fontSize: 12)),
                Text(
                  '${_products.length}',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Tab(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Active', style: TextStyle(fontSize: 12)),
                Text(
                  '${_products.where((p) => p['is_active'] == true).length}',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Tab(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Pending', style: TextStyle(fontSize: 12)),
                Text(
                  '${_products.where((p) => p['approval_status'] == 'pending').length}',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Tab(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Approved', style: TextStyle(fontSize: 12)),
                Text(
                  '${_products.where((p) => p['approval_status'] == 'approved').length}',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF059669)),
      ),
    );
  }

  Widget _buildProductList() {
    final filteredProducts = _filteredProducts;

    if (filteredProducts.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredProducts.length,
      itemBuilder: (context, index) {
        final product = filteredProducts[index];
        return _buildProductCard(product);
      },
    );
  }

  Widget _buildEmptyState() {
    String title;
    String subtitle;

    switch (_selectedFilter) {
      case 'active':
        title = 'No active products';
        subtitle = 'Products that are approved and active will appear here';
        break;
      case 'pending':
        title = 'No pending products';
        subtitle = 'Products waiting for approval will appear here';
        break;
      case 'approved':
        title = 'No approved products';
        subtitle = 'Products that have been approved will appear here';
        break;
      default:
        title = 'No products yet';
        subtitle = 'Add your first product to get started';
    }

    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey[200]!, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            if (_selectedFilter == 'all') ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  _showAddProductDialog();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF059669),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                icon: const Icon(Icons.add),
                label: const Text('Add Your First Product'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF059669).withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: const Color(0xFF059669).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.fastfood,
                    color: Color(0xFF059669),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product['name'] ?? 'Product',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '₹${product['price'] ?? 0}/kg',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Color(0xFF059669),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      product['approval_status'],
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getStatusColor(product['approval_status']),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _getStatusText(product['approval_status']),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getStatusColor(product['approval_status']),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Product Details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (product['description'] != null) ...[
                  Text(
                    product['description'],
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                ],
                Row(
                  children: [
                    _buildInfoChip(
                      Icons.category_outlined,
                      product['category'] ?? 'Meat',
                      Colors.blue,
                    ),
                    const SizedBox(width: 8),
                    _buildInfoChip(
                      product['is_active'] == true
                          ? Icons.visibility
                          : Icons.visibility_off,
                      product['is_active'] == true ? 'Active' : 'Inactive',
                      product['is_active'] == true
                          ? Colors.green
                          : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    if (product['odoo_product_id'] != null)
                      _buildInfoChip(Icons.sync, 'Synced', Colors.purple),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          _showEditProductDialog(product);
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF059669),
                          side: const BorderSide(color: Color(0xFF059669)),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.edit, size: 16),
                        label: const Text('Edit'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _toggleProductStatus(product);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: product['is_active'] == true
                              ? Colors.orange
                              : const Color(0xFF059669),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: Icon(
                          product['is_active'] == true
                              ? Icons.pause
                              : Icons.play_arrow,
                          size: 16,
                        ),
                        label: Text(
                          product['is_active'] == true
                              ? 'Deactivate'
                              : 'Activate',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return const Color(0xFF10B981);
      case 'pending':
        return const Color(0xFFF59E0B);
      case 'rejected':
        return const Color(0xFFEF4444);
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Pending';
      case 'rejected':
        return 'Rejected';
      default:
        return status?.toUpperCase() ?? 'Unknown';
    }
  }

  Future<void> _refreshProducts() async {
    setState(() {
      _isLoading = true;
    });

    // Show refresh feedback
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Refreshing products from Odoo and Supabase...'),
        backgroundColor: Color(0xFF059669),
        duration: Duration(seconds: 2),
      ),
    );

    await _loadProducts();
  }

  void _onFilterChanged(ProductFilter newFilter) {
    setState(() {
      _currentFilter = newFilter;
    });
    _loadProducts();
  }

  Future<void> _syncWithOdoo() async {
    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 16),
            Text('Syncing approval status with Odoo...'),
          ],
        ),
        backgroundColor: Color(0xFF059669),
        duration: Duration(seconds: 30), // Longer duration for sync
      ),
    );

    try {
      // Sync approval status for this seller's products
      final syncResult = await _syncService.syncAllProductStatus(
        sellerId: widget.seller['id'],
        showLogs: true,
      );

      // Hide loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      if (syncResult['success']) {
        final updatedCount = syncResult['updated_count'] ?? 0;
        final totalCount = syncResult['total_products'] ?? 0;
        final errors = syncResult['errors'] as List<String>? ?? [];

        // Show success message
        String message = 'Sync completed! ';
        if (updatedCount > 0) {
          message += '$updatedCount of $totalCount products updated.';
        } else {
          message += 'All products are up to date.';
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(message),
                  if (errors.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Errors: ${errors.length}',
                      style: const TextStyle(color: Colors.orange),
                    ),
                  ],
                ],
              ),
              backgroundColor: updatedCount > 0
                  ? const Color(0xFF059669)
                  : Colors.blue,
              duration: const Duration(seconds: 4),
              action: errors.isNotEmpty
                  ? SnackBarAction(
                      label: 'Details',
                      textColor: Colors.white,
                      onPressed: () => _showSyncErrorDialog(errors),
                    )
                  : null,
            ),
          );
        }

        // Refresh the product list to show updated statuses
        await _loadProducts();
      } else {
        throw Exception(syncResult['error'] ?? 'Unknown sync error');
      }
    } catch (e) {
      // Hide loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _showSyncErrorDialog(List<String> errors) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sync Errors'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: errors.length,
            itemBuilder: (context, index) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Text(
                '• ${errors[index]}',
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Initialize background sync automation with feature flag control
  Future<void> _initializeBackgroundSync() async {
    try {
      // Check if background sync is enabled via feature flag
      final isEnabled = await FeatureFlags.isEnabledRemote(
        'background_sync_automation',
      );

      if (isEnabled) {
        _isBackgroundSyncEnabled = true;
        _startBackgroundSync();
        print('🔄 BACKGROUND SYNC - Automation enabled (30 second intervals)');
      } else {
        print('ℹ️ BACKGROUND SYNC - Automation disabled via feature flag');
      }
    } catch (e) {
      print('❌ BACKGROUND SYNC - Failed to initialize: $e');
    }
  }

  /// Start the background sync timer
  void _startBackgroundSync() {
    if (!_isBackgroundSyncEnabled) return;

    _backgroundSyncTimer?.cancel();
    _backgroundSyncTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _performBackgroundSync();
    });
  }

  /// Perform background sync with performance optimization
  Future<void> _performBackgroundSync() async {
    if (!mounted || !_isBackgroundSyncEnabled) return;

    try {
      // Only sync if there are pending products to avoid unnecessary API calls
      final needsSync = await _syncService.isSyncNeeded(
        sellerId: widget.seller['id'],
      );

      if (needsSync) {
        print('🔄 BACKGROUND SYNC - Syncing pending products...');

        final syncResult = await _syncService.syncAllProductStatus(
          sellerId: widget.seller['id'],
          showLogs: false, // Silent background sync
        );

        if (syncResult['success'] && syncResult['updated_count'] > 0) {
          print(
            '✅ BACKGROUND SYNC - Updated ${syncResult['updated_count']} products',
          );

          // Refresh the UI if products were updated
          if (mounted) {
            _loadProducts();
          }
        }
      }
    } catch (e) {
      print('❌ BACKGROUND SYNC - Error: $e');
    }
  }

  /// Stop background sync (called on dispose)
  void _stopBackgroundSync() {
    _backgroundSyncTimer?.cancel();
    _backgroundSyncTimer = null;
    _isBackgroundSyncEnabled = false;
  }

  void _showAddProductDialog() {
    showDialog(
      context: context,
      builder: (context) => AddProductDialog(
        seller: widget.seller,
        onProductAdded: () {
          _loadProducts(); // Refresh the product list
        },
      ),
    );
  }

  void _showEditProductDialog(Map<String, dynamic> product) {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        product: product,
        onProductUpdated: () {
          _loadProducts(); // Refresh the product list
        },
      ),
    );
  }

  Future<void> _toggleProductStatus(Map<String, dynamic> product) async {
    final isActive = product['is_active'] == true;
    final canToggle = product['approval_status'] == 'approved';

    // Business rule validation - only approved products can be activated
    if (!canToggle && !isActive) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Only approved products can be activated'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Show confirmation dialog
    _showActivateDeactivateDialog(product);
  }

  void _showActivateDeactivateDialog(Map<String, dynamic> product) {
    final isActive = product['is_active'] == true;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${isActive ? 'Deactivate' : 'Activate'} Product'),
        content: Text(
          'Are you sure you want to ${isActive ? 'deactivate' : 'activate'} "${product['name']}"?\n\n'
          '${isActive ? 'Customers will no longer see this product.' : 'This product will be visible to customers.'}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performToggleProductActive(product);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isActive
                  ? Colors.orange
                  : const Color(0xFF059669),
            ),
            child: Text(isActive ? 'Deactivate' : 'Activate'),
          ),
        ],
      ),
    );
  }

  Future<void> _performToggleProductActive(Map<String, dynamic> product) async {
    final newActiveState = !(product['is_active'] == true);

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Text(
              '${newActiveState ? 'Activating' : 'Deactivating'} product...',
            ),
          ],
        ),
        backgroundColor: const Color(0xFF059669),
        duration: const Duration(seconds: 30),
      ),
    );

    try {
      final result = await _odooService.toggleProductActive(
        product['id'],
        newActiveState,
      );

      // Hide loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: const Color(0xFF059669),
            ),
          );

          // Refresh the product list
          await _loadProducts();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Failed to update product'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Hide loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating product: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class EditProductDialog extends StatefulWidget {
  final Map<String, dynamic> product;
  final VoidCallback onProductUpdated;

  const EditProductDialog({
    super.key,
    required this.product,
    required this.onProductUpdated,
  });

  @override
  State<EditProductDialog> createState() => _EditProductDialogState();
}

class _EditProductDialogState extends State<EditProductDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _priceController;
  late TextEditingController _descriptionController;

  bool _isSubmitting = false;
  bool _willRequireReapproval = false;

  final OdooService _odooService = OdooService();

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.product['name']);
    _priceController = TextEditingController(
      text: widget.product['price'].toString(),
    );
    _descriptionController = TextEditingController(
      text: widget.product['description'] ?? '',
    );

    _checkReapprovalRequired();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _checkReapprovalRequired() {
    // If product is approved and user makes changes, it will need re-approval
    _willRequireReapproval = widget.product['approval_status'] == 'approved';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Product'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_willRequireReapproval)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.orange),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Editing this approved product will require re-approval',
                          style: TextStyle(color: Colors.orange[800]),
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Product Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                    value?.isEmpty == true ? 'Name required' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'Price (₹/kg)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value?.isEmpty == true) return 'Price required';
                  if (double.tryParse(value!) == null) return 'Invalid price';
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isSubmitting ? null : _updateProduct,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF059669),
          ),
          child: _isSubmitting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : const Text(
                  'Update Product',
                  style: TextStyle(color: Colors.white),
                ),
        ),
      ],
    );
  }

  Future<void> _updateProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final updates = {
        'name': _nameController.text.trim(),
        'price': double.parse(_priceController.text.trim()),
        'description': _descriptionController.text.trim(),
      };

      final result = await _odooService.updateProductLocal(
        widget.product['id'],
        updates,
      );

      if (mounted) {
        if (result['success']) {
          Navigator.pop(context);
          widget.onProductUpdated();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: const Color(0xFF059669),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Failed to update product'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating product: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

class AddProductDialog extends StatefulWidget {
  final Map<String, dynamic> seller;
  final VoidCallback onProductAdded;

  const AddProductDialog({
    super.key,
    required this.seller,
    required this.onProductAdded,
  });

  @override
  State<AddProductDialog> createState() => _AddProductDialogState();
}

class _AddProductDialogState extends State<AddProductDialog> {
  List<String> _pendingImageUrls = [];
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _descriptionController = TextEditingController();

  final SupabaseService _supabaseService = SupabaseService();
  final OdooService _odooService = OdooService();
  bool _isSubmitting = false;
  String _productType = 'meat';
  String? _meatCategory;
  final List<String> _meatCategories = const [
    'Chicken',
    'Mutton',
    'Fish',
    'Eggs',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFF059669),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.add_box, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Add Meat Product',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // Form
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Type selector (UI-only)
                      const Text(
                        'Product Type',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: [
                          ChoiceChip(
                            label: const Text('Meat'),
                            selected: _productType == 'meat',
                            onSelected: (sel) {
                              setState(() {
                                _productType = 'meat';
                              });
                            },
                          ),
                          ChoiceChip(
                            label: const Text('Livestock'),
                            selected: _productType == 'livestock',
                            onSelected: (sel) {
                              setState(() {
                                _productType = 'livestock';
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_productType == 'meat')
                        DropdownButtonFormField<String>(
                          value: _meatCategory,
                          items: _meatCategories
                              .map(
                                (c) => DropdownMenuItem<String>(
                                  value: c,
                                  child: Text(c),
                                ),
                              )
                              .toList(),
                          onChanged: (v) => setState(() => _meatCategory = v),
                          decoration: const InputDecoration(
                            labelText: 'Meat Subcategory',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      if (_productType == 'meat') const SizedBox(height: 16),

                      _buildTextField(
                        controller: _nameController,
                        label: 'Product Name *',
                        hint: 'e.g., Chicken Breast',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Product name is required';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: _priceController,
                        label: 'Price (₹) *',
                        hint: '0.00',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Price is required';
                          }
                          final price = double.tryParse(value);
                          if (price == null || price <= 0) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Quantity field removed visually (kept out to avoid confusion). Stock defaults handled server-side.

                      // UOM display (read-only UI)
                      Row(
                        children: const [
                          Text(
                            'Unit of Measure: ',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                          SizedBox(width: 6),
                          Chip(label: Text('kg')),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: _descriptionController,
                        label: 'Description',
                        hint: 'Product description...',
                        maxLines: 4,
                      ),
                      const SizedBox(height: 16),
                      // Feature-flagged image picker (zero-risk, UI-only)
                      if (UiFlags.enableSellerProductImages)
                        ProductImagePickerPanel(
                          sellerId: widget.seller['id'] as String,
                          onChanged: (urls) {
                            // Stored locally; will be passed to createProduct below
                            _pendingImageUrls = urls;
                          },
                        )
                      else
                        _buildImageUploadSection(),
                    ],
                  ),
                ),
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isSubmitting
                          ? null
                          : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey[600],
                        side: BorderSide(color: Colors.grey[300]!),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isSubmitting ? null : _submitProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF059669),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isSubmitting
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Text('Add Product'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF059669), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Product Images',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey[300]!,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[50],
          ),
          child: Column(
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 12),
              Text(
                'Choose Files',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'No file chosen',
                style: TextStyle(fontSize: 12, color: Colors.grey[500]),
              ),
              const SizedBox(height: 12),
              Text(
                'Take live photos using your device camera (optional)',
                style: TextStyle(fontSize: 11, color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _submitProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Debug: Check what seller data we have
      print('🔍 DEBUG - widget.seller data: ${widget.seller}');
      print(
        '🔍 DEBUG - seller id: "${widget.seller['id']}" (${widget.seller['id'].runtimeType})',
      );
      print(
        '🔍 DEBUG - seller name: "${widget.seller['seller_name']}" (${widget.seller['seller_name'].runtimeType})',
      );

      // Create product using OdooService (handles both local and Odoo creation)
      final odooResult = await _odooService.createProduct(
        name: _nameController.text.trim(),
        price: double.parse(_priceController.text.trim()),
        sellerId: widget.seller['id'], // ✅ UUID for database
        sellerUid: widget.seller['id'], // ✅ UUID for Odoo
        sellerName: widget.seller['seller_name'] ?? 'Unknown Seller',
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        imageUrls: UiFlags.enableSellerProductImages ? _pendingImageUrls : null,
        nutritionalInfo: {
          // Use JSONB extension fields without schema changes
          'extra': {
            'product_type': _productType,
            if (_productType == 'meat' && _meatCategory != null)
              'meat_category': _meatCategory,
            'uom': 'kg',
          },
        },
      );

      if (mounted) {
        Navigator.of(context).pop();

        if (odooResult['success']) {
          final isOdooSynced = odooResult['odoo_sync'] != false;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isOdooSynced
                    ? 'Product submitted for approval successfully!'
                    : 'Product created locally. Odoo sync will be retried later.',
              ),
              backgroundColor: isOdooSynced
                  ? const Color(0xFF059669)
                  : Colors.orange,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to create product: ${odooResult['message']}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }

        widget.onProductAdded();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding product: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
